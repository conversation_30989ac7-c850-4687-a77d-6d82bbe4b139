package com.ctrip.dcs.dsp.delay.util;

import com.ctrip.framework.foundation.Env;
import com.ctrip.framework.foundation.Foundation;
import com.ctrip.igt.framework.common.metrics.DefaultHickwallReporterConfig;
import com.ctrip.igt.framework.common.metrics.HickwallReporterConfig;
import com.ctrip.ops.hickwall.HickwallUDPReporter;
import io.dropwizard.metrics5.MetricRegistry;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public final class MetricsFactory {

    private static volatile MetricRegistry metricRegistry = null;
    private static DefaultHickwallReporterConfig _instance;

    /**
     * 获取MetricRegistry实例
     *
     * @return
     */
    public static MetricRegistry getMetricRegistry() {
        if (metricRegistry == null) {
            synchronized (MetricsFactory.class) {
                if (metricRegistry == null) {
                    HickwallReporterConfig config = getDefaultHickwallReporterConfig();
                    metricRegistry = new MetricRegistry();
                    // 非本地模式下才激活
                    if (Foundation.server().getEnv() != Env.LOCAL) {
                        HickwallUDPReporter.enable(metricRegistry, 10, TimeUnit.SECONDS, config.getDbName());
                    }
                    return metricRegistry;
                }
            }
        }
        return metricRegistry;
    }

    private static HickwallReporterConfig getDefaultHickwallReporterConfig(){
        if(_instance!=null){
            return _instance;
        }
        _instance = new DefaultHickwallReporterConfig();
        return _instance;

    }
}

