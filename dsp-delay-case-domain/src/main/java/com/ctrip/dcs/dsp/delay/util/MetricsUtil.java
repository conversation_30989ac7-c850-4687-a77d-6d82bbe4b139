package com.ctrip.dcs.dsp.delay.util;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.metrics.MetricsFactory;
import com.google.common.collect.Maps;
import io.dropwizard.metrics5.*;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class MetricsUtil {

    private static final Logger logger = LoggerFactory.getLogger(MetricsUtil.class);

    private static final String APP_ID = "100036007";

    private static final String METRICS_NAME_PREFIX = "dcs.dsp.delay.case.";

    private static final MetricRegistry METRIC_REGISTRY = MetricsFactory.getMetricRegistry();
    private static final MetricRegistry METRIC_REGISTRY1 = com.ctrip.dcs.dsp.delay.util.MetricsFactory.getMetricRegistry();

    public static void recordValue(String name, long value) {
        String key = METRICS_NAME_PREFIX + name;
        try{
            Map<String,String> tags = new HashMap<>();
            tags.put("appId", APP_ID);
            MetricName metricName = new MetricName(key, tags);
            ResetCounter resetCounter = METRIC_REGISTRY.resetCounter(metricName);
            resetCounter.inc(value);
            resetCounter.setZero(true);
        }catch (Exception e){
            logger.warn("metric resetCount error",e);
        }
    }

    public static void recordValueWithTag(String name, long value, String tagKey, String tagValue) {
        String key = METRICS_NAME_PREFIX + name;
        try{
            Map<String,String> tags = new HashMap<>();
            tags.put(tagKey, tagValue);
            MetricName metricName = new MetricName(key, tags);
            ResetCounter resetCounter = METRIC_REGISTRY.resetCounter(metricName);
            resetCounter.inc(value);
            resetCounter.setZero(true);
        }catch (Exception e){
            logger.warn("metric resetCount error",e);
        }
    }

    public static void recordTimeWithTag(String name, long duration, String tagKey, String tagValue) {
        String key = METRICS_NAME_PREFIX + name;
        try{
            Map<String,String> tags = new HashMap<>();
            tags.put(tagKey, tagValue);
            MetricName metricName = new MetricName(key, tags);
            ResetTimer restTimer = METRIC_REGISTRY.resetTimer(metricName);
            restTimer.update(duration, TimeUnit.MILLISECONDS);
        }catch (Exception e){
            logger.warn("metric recordTimeWithTag error",e);
        }
    }

    public static void recordTime(String name, long duration) {
        String key = METRICS_NAME_PREFIX + name;
        try{
            Map<String,String> tags = new HashMap<>();
            tags.put("appId", APP_ID);
            MetricName metricName = new MetricName(key, tags);
            ResetTimer restTimer = METRIC_REGISTRY.resetTimer(metricName);
            restTimer.update(duration, TimeUnit.MILLISECONDS);
        }catch (Exception e){
            logger.warn("metric resetTime error",e);
        }
    }

    public static void gaugeValue(String name, Integer value) {
        String key = METRICS_NAME_PREFIX + name;
        try{
            Map<String,String> tags = new HashMap<>();
            tags.put("appId", APP_ID);
            MetricName metricName = new MetricName(key, tags);

            Gauge<Integer> guage = () -> value;
            METRIC_REGISTRY.gauge(metricName,() -> guage);
        }catch (Exception e){
            logger.warn("metric gaugeValue error",e);
        }
    }

    public static void qpsRate(String name, long value) {
        String key = METRICS_NAME_PREFIX + name;
        try{
            Map<String,String> tags = new HashMap<>();
            tags.put("appId", APP_ID);
            Meter meter = METRIC_REGISTRY.meter(new MetricName(key, tags));
            meter.mark(value);
        }catch (Exception e){
            logger.warn("metric resetCount error",e);
        }
    }

    public static void qpsRateWithTag(String name, long value, String tagKey, String tagValue) {
        String key = METRICS_NAME_PREFIX + name;
        Map<String, String> tagMap = Maps.newHashMapWithExpectedSize(2);
        if (StringUtils.isNotBlank(tagValue)) {
            tagMap.put(tagKey, tagValue);
        }
        Meter meter = METRIC_REGISTRY.meter(new MetricName(key, tagMap));
        meter.mark(value);
    }

    public static void countValue(String name, long value) {
        String key = METRICS_NAME_PREFIX + name;
        try{
            Map<String,String> tags = new HashMap<>();
            tags.put("appId", APP_ID);
            MetricName metricName = new MetricName(key, tags);
            ResetCounter resetCounter = METRIC_REGISTRY1.resetCounter(metricName);
            resetCounter.inc(value);
        }catch (Exception e){
            logger.warn("metric resetCount error",e);
        }
    }

    public static void countValueWithTag(String name, long value, String tagKey, String tagValue) {
        String key = METRICS_NAME_PREFIX + name;
        try{
            Map<String,String> tags = new HashMap<>();
            tags.put(tagKey, tagValue);
            MetricName metricName = new MetricName(key, tags);
            ResetCounter resetCounter = METRIC_REGISTRY1.resetCounter(metricName);
            resetCounter.inc(value);
        }catch (Exception e){
            logger.warn("metric resetCount error",e);
        }
    }
}
