package com.ctrip.dcs.dsp.delay.qconfig

import com.ctrip.dcs.dsp.delay.util.DateUtil
import com.ctrip.dcs.dsp.delay.util.JsonUtil
import spock.lang.*

/**
 * <AUTHOR>
 */
class DelayDspCommonQConfigTest extends Specification {


    def "test get Delay Dsp Channel Set"() {
        given:
        DelayDspCommonQConfig config1 = new DelayDspCommonQConfig(delayDspChannel: "")
        DelayDspCommonQConfig config2 = new DelayDspCommonQConfig(delayDspChannel: "1,2")

        when:
        Set<Long> result1 = config1.getDelayDspChannelSet()
        Set<Long> result2 = config2.getDelayDspChannelSet()

        then:
        result1 == [] as Set<Long>
        result2 == [1l, 2l] as Set<Long>
    }

    def "test get Not Delay Dsp Channel Set"() {
        given:
        DelayDspCommonQConfig config1 = new DelayDspCommonQConfig(notDelayDspChannel: "")
        DelayDspCommonQConfig config2 = new DelayDspCommonQConfig(notDelayDspChannel: "1,2")

        when:
        Set<Long> result1 = config1.getNotDelayDspChannelSet()
        Set<Long> result2 = config2.getNotDelayDspChannelSet()

        then:
        result1 == [] as Set<Long>
        result2 == [1l, 2l] as Set<Long>
    }

    def "test get Delay Dsp Validators List"() {
        given:
        DelayDspCommonQConfig config1 = new DelayDspCommonQConfig(delayDspValidators: "")
        DelayDspCommonQConfig config2 = new DelayDspCommonQConfig(delayDspValidators: "1,2")

        when:
        List<String> result1 = config1.getDelayDspValidatorsList()
        List<String> result2 = config2.getDelayDspValidatorsList()

        then:
        result1 == [] as List<String>
        result2 == ["1", "2"] as List<String>
    }

    def "test get Delay Taken Validators List"() {
        given:
        DelayDspCommonQConfig config1 = new DelayDspCommonQConfig(delayTakenValidators: "")
        DelayDspCommonQConfig config2 = new DelayDspCommonQConfig(delayTakenValidators: "1,2")

        when:
        List<String> result1 = config1.getDelayTakenValidatorsList()
        List<String> result2 = config2.getDelayTakenValidatorsList()

        then:
        result1 == [] as List<String>
        result2 == ["1", "2"] as List<String>
    }

    def "test get Driver Filter List"() {

        given:
        DelayDspCommonQConfig config1 = new DelayDspCommonQConfig(driverFilters: "")
        DelayDspCommonQConfig config2 = new DelayDspCommonQConfig(driverFilters: "1,2")

        when:
        List<String> result1 = config1.getDriverFilterList()
        List<String> result2 = config2.getDriverFilterList()

        then:
        result1 == [] as List<String>
        result2 == ["1", "2"] as List<String>
    }

    def "test get User White List"() {
        given:
        DelayDspCommonQConfig config1 = new DelayDspCommonQConfig(userWhite: "")
        DelayDspCommonQConfig config2 = new DelayDspCommonQConfig(userWhite: "1,2")

        when:
        List<String> result1 = config1.getUserWhiteList()
        List<String> result2 = config2.getUserWhiteList()

        then:
        result1 == [] as List<String>
        result2 == ["1", "2"] as List<String>
    }

    def "test get Driver To Order Check List"() {
        given:
        DelayDspCommonQConfig config1 = new DelayDspCommonQConfig(driverToOrderChecks: "")
        DelayDspCommonQConfig config2 = new DelayDspCommonQConfig(driverToOrderChecks: "1,2")

        when:
        List<String> result1 = config1.getDriverToOrderCheckList()
        List<String> result2 = config2.getDriverToOrderCheckList()

        then:
        result1 == [] as List<String>
        result2 == ["1", "2"] as List<String>
    }

    def "test get Order To Order Check List"() {
        given:
        DelayDspCommonQConfig config1 = new DelayDspCommonQConfig(orderToOrderChecks: "")
        DelayDspCommonQConfig config2 = new DelayDspCommonQConfig(orderToOrderChecks: "1,2")

        when:
        List<String> result1 = config1.getOrderToOrderCheckList()
        List<String> result2 = config2.getOrderToOrderCheckList()

        then:
        result1 == [] as List<String>
        result2 == ["1", "2"] as List<String>
    }

    def "test get Order To Taken Check List"() {
        given:
        DelayDspCommonQConfig config1 = new DelayDspCommonQConfig(orderToTakenChecks: "")
        DelayDspCommonQConfig config2 = new DelayDspCommonQConfig(orderToTakenChecks: "1,2")

        when:
        List<String> result1 = config1.getOrderToTakenCheckList()
        List<String> result2 = config2.getOrderToTakenCheckList()

        then:
        result1 == [] as List<String>
        result2 == ["1", "2"] as List<String>
    }

    def "test is Dispatcher Order City Switch"() {
        given:
        DelayDspCommonQConfig config1 = new DelayDspCommonQConfig(dispatcherOrderCitySwitch: "")
        DelayDspCommonQConfig config2 = new DelayDspCommonQConfig(dispatcherOrderCitySwitch: "1,2")
        DelayDspCommonQConfig config3 = new DelayDspCommonQConfig(dispatcherOrderCitySwitch: "all")

        when:
        Boolean result1 = config1.isDispatcherOrderCitySwitch(null)
        Boolean result2 = config1.isDispatcherOrderCitySwitch(0)
        Boolean result3 = config1.isDispatcherOrderCitySwitch(0)
        Boolean result4 = config2.isDispatcherOrderCitySwitch(1)
        Boolean result5 = config3.isDispatcherOrderCitySwitch(1)

        then:
        result1 != true
        result2 != true
        result3 != true
        result4 == true
        result5 == true
    }

    def "test is Order Profit City"() {
        given:
        DelayDspCommonQConfig config1 = new DelayDspCommonQConfig(orderProfitCityIds: "")
        DelayDspCommonQConfig config2 = new DelayDspCommonQConfig(orderProfitCityIds: "1,2")
        DelayDspCommonQConfig config3 = new DelayDspCommonQConfig(orderProfitCityIds: "all")

        when:
        Boolean result1 = config1.isOrderProfitCity(0)
        Boolean result2 = config2.isOrderProfitCity(null)
        Boolean result3 = config2.isOrderProfitCity(1)
        Boolean result4 = config3.isOrderProfitCity(1)

        then:
        result1 != true
        result2 != true
        result3 == true
        result4 == true
    }


    def "test getOutReasonConfig"() {
        given:
        Map<String, String> outPoolReason = new HashMap<>()
        outPoolReason.put("1", "1")
        DelayDspCommonQConfig config1 = new DelayDspCommonQConfig(outPoolReason: JsonUtil.toJson(outPoolReason))

        when:
        String result1 = config1.getOutReasonConfig(null)
        String result2 = config1.getOutReasonConfig("1")

        then:
        result1.equals("") == true
        result2.equals("") != true
    }

    def "test getShortDisOrderCategoryCodeSet"() {
        given:
        Map<String, String> shortDisOrderCategoryCode = new HashMap<>()
        shortDisOrderCategoryCode.put("1", "airport_pickup,airport_dropoff,station_pickup,station_dropoff")
        shortDisOrderCategoryCode.put("2", "")
        DelayDspCommonQConfig config1 = new DelayDspCommonQConfig(shortDisOrderCategoryCode: JsonUtil.toJson(shortDisOrderCategoryCode))

        when:
        boolean result1 = config1.getShortDisOrderCategoryCodeSet(1,"AIRPORT_PICKUP")
        boolean result2 = config1.getShortDisOrderCategoryCodeSet(2,"STATION_PICKUP")

        then:
        result1 == true
        result2 == false
    }

    @Unroll
    def "test isWithinCallableTimePeriod with multiple time periods"() {
        given:
        DelayDspCommonQConfig config = new DelayDspCommonQConfig(
            callableTimePeriods: timePeriods
        )
        Date testDate = DateUtil.parseDate("2023-12-25 " + testTime + ":00")

        when:
        boolean result = config.isWithinCallableTimePeriod(testDate)

        then:
        result == expected

        where:
        timePeriods                    | testTime || expected
        "08:00-12:00,14:00-18:00"     | "10:00"  || true   // 在第一个时段内
        "08:00-12:00,14:00-18:00"     | "16:00"  || true   // 在第二个时段内
        "08:00-12:00,14:00-18:00"     | "13:00"  || false  // 在时段间隙
        "08:00-12:00,14:00-18:00"     | "07:00"  || false  // 在所有时段之前
        "08:00-12:00,14:00-18:00"     | "19:00"  || false  // 在所有时段之后
        "08:00-12:00,14:00-18:00"     | "08:00"  || true   // 边界值
        "08:00-12:00,14:00-18:00"     | "12:00"  || true   // 边界值
        "08:00-12:00,14:00-18:00"     | "14:00"  || true   // 边界值
        "08:00-12:00,14:00-18:00"     | "18:00"  || true   // 边界值
        "22:00-02:00,08:00-12:00"     | "10:00"  || true   // 正常时段
        "22:00-02:00,08:00-12:00"     | "05:00"  || false  // 时段间隙
        "09:00-09:00"                 | "09:00"  || true   // 单点时间
    }

    def "test isWithinCallableTimePeriod with invalid time period format"() {
        given:
        DelayDspCommonQConfig config = new DelayDspCommonQConfig(
            callableTimePeriods: "08:00-12:00,invalid-format,14:00-18:00"
        )
        Date testDate = DateUtil.parseDate("2023-12-25 16:00:00")

        when:
        boolean result = config.isWithinCallableTimePeriod(testDate)

        then:
        result == true  // 应该在有效的第二个时段内
    }

    def "test isWithinCallableTimePeriod with null date"() {
        given:
        DelayDspCommonQConfig config = new DelayDspCommonQConfig(
            callableTimePeriods: "08:00-18:00"
        )

        when:
        boolean result = config.isWithinCallableTimePeriod(null)

        then:
        result == false  // 异常情况默认允许
    }
}

