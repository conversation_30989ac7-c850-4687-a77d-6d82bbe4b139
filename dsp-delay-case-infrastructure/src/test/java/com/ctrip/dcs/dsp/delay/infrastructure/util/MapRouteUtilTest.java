package com.ctrip.dcs.dsp.delay.infrastructure.util;

import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.DcsMapDomainServiceProxy;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.location.application.service.message.QueryPredictRouteResponseType;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.igt.framework.infrastructure.message.ResponseResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * MapRouteUtil单元测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class MapRouteUtilTest {

    @Mock
    private DcsMapDomainServiceProxy dcsMapDomainServiceProxy;

    @Mock
    private MapRouteConfig mapRouteConfig;

    @Mock
    private GeoGateway geoGateway;

    @Mock
    private ExecutorService delayGaoDeFutureThreadPool;

    @InjectMocks
    private MapRouteUtil mapRouteUtil;

    private MapRouteUtil.RouteRequest testRequest;
    private QueryPredictRouteResponseType mockResponse;

    @BeforeEach
    void setUp() {
        // 创建测试请求
        testRequest = createTestRequest();
        
        // 创建模拟响应
        mockResponse = createMockResponse();
        
        // 使用真实的线程池进行测试
        when(delayGaoDeFutureThreadPool).thenReturn(Executors.newFixedThreadPool(2));
    }

    @Test
    void testQueryRoute_Success() {
        // 准备测试数据
        when(mapRouteConfig.shouldUseGaodeFuture(anyInt(), any(Date.class))).thenReturn(true);
        when(dcsMapDomainServiceProxy.queryEstimateRoute(any())).thenReturn(mockResponse);

        // 执行测试
        MapRouteUtil.RouteResponse response = mapRouteUtil.queryRoute(testRequest);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(10.0, response.getDistance()); // 10000m / 1000 = 10km
        assertEquals(30.0, response.getDuration());  // 1800s / 60 = 30min
        assertEquals(116.397128, response.getFromLongitude());
        assertEquals(39.916527, response.getFromLatitude());
    }

    @Test
    void testQueryRoute_FallbackToBatch() {
        // 准备测试数据 - 不满足高德未来条件
        when(mapRouteConfig.shouldUseGaodeFuture(anyInt(), any(Date.class))).thenReturn(false);
        
        Route mockRoute = new Route("test_hash", 15.0, 25.0);
        when(geoGateway.queryRoutes(anyInt(), anyList())).thenReturn(Arrays.asList(mockRoute));

        // 执行测试
        MapRouteUtil.RouteResponse response = mapRouteUtil.queryRoute(testRequest);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(15.0, response.getDistance());
        assertEquals(25.0, response.getDuration());
        
        // 验证调用了批量方法
        verify(geoGateway).queryRoutes(eq(1), anyList());
        verify(dcsMapDomainServiceProxy, never()).queryEstimateRoute(any());
    }

    @Test
    void testQueryRoute_InvalidRequest() {
        // 测试空请求
        MapRouteUtil.RouteResponse response = mapRouteUtil.queryRoute(null);
        
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("Request cannot be null", response.getErrorMessage());
    }

    @Test
    void testQueryRoute_InvalidParameters() {
        // 创建无效请求（缺少经纬度）
        MapRouteUtil.RouteRequest invalidRequest = new MapRouteUtil.RouteRequest();
        MapRouteUtil.MapPoint fromPoint = new MapRouteUtil.MapPoint();
        fromPoint.setCityId(1);
        // 故意不设置经纬度
        invalidRequest.setFromPoint(fromPoint);

        MapRouteUtil.RouteResponse response = mapRouteUtil.queryRoute(invalidRequest);
        
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("Invalid request parameters", response.getErrorMessage());
    }

    @Test
    void testQueryRoute_ServiceError() {
        // 准备测试数据
        when(mapRouteConfig.shouldUseGaodeFuture(anyInt(), any(Date.class))).thenReturn(true);
        when(dcsMapDomainServiceProxy.queryEstimateRoute(any())).thenReturn(null);

        // 执行测试
        MapRouteUtil.RouteResponse response = mapRouteUtil.queryRoute(testRequest);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertTrue(response.getErrorMessage().contains("Response is null"));
    }

    @Test
    void testQueryRoutesBatch_Success() {
        // 准备测试数据
        List<MapRouteUtil.RouteRequest> requests = Arrays.asList(testRequest, createTestRequest());
        when(mapRouteConfig.shouldUseGaodeFuture(anyInt(), any(Date.class))).thenReturn(true);
        when(dcsMapDomainServiceProxy.queryEstimateRoute(any())).thenReturn(mockResponse);

        // 使用同步执行来简化测试
        ExecutorService syncExecutor = Executors.newSingleThreadExecutor();
        when(delayGaoDeFutureThreadPool).thenReturn(syncExecutor);

        // 执行测试
        List<MapRouteUtil.RouteResponse> responses = mapRouteUtil.queryRoutesBatch(requests);

        // 验证结果
        assertNotNull(responses);
        assertEquals(2, responses.size());
        
        for (MapRouteUtil.RouteResponse response : responses) {
            assertTrue(response.isSuccess());
            assertEquals(10.0, response.getDistance());
            assertEquals(30.0, response.getDuration());
        }
        
        syncExecutor.shutdown();
    }

    @Test
    void testQueryRoutesBatch_EmptyList() {
        // 测试空列表
        List<MapRouteUtil.RouteResponse> responses = mapRouteUtil.queryRoutesBatch(Arrays.asList());
        
        assertNotNull(responses);
        assertTrue(responses.isEmpty());
    }

    @Test
    void testQueryRoutesBatch_NullList() {
        // 测试null列表
        List<MapRouteUtil.RouteResponse> responses = mapRouteUtil.queryRoutesBatch(null);
        
        assertNotNull(responses);
        assertTrue(responses.isEmpty());
    }

    @Test
    void testFallbackToBatchMethod_EmptyRoutes() {
        // 准备测试数据 - 批量方法返回空结果
        when(mapRouteConfig.shouldUseGaodeFuture(anyInt(), any(Date.class))).thenReturn(false);
        when(geoGateway.queryRoutes(anyInt(), anyList())).thenReturn(Arrays.asList());

        // 执行测试
        MapRouteUtil.RouteResponse response = mapRouteUtil.queryRoute(testRequest);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertTrue(response.getErrorMessage().contains("empty routes"));
    }

    @Test
    void testServiceResponseError() {
        // 准备测试数据 - 服务返回错误码
        when(mapRouteConfig.shouldUseGaodeFuture(anyInt(), any(Date.class))).thenReturn(true);
        
        QueryPredictRouteResponseType errorResponse = new QueryPredictRouteResponseType();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode("ERROR_CODE");
        errorResponse.setResponseResult(responseResult);
        
        when(dcsMapDomainServiceProxy.queryEstimateRoute(any())).thenReturn(errorResponse);

        // 执行测试
        MapRouteUtil.RouteResponse response = mapRouteUtil.queryRoute(testRequest);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertTrue(response.getErrorMessage().contains("Service call failed"));
    }

    /**
     * 创建测试请求
     */
    private MapRouteUtil.RouteRequest createTestRequest() {
        MapRouteUtil.MapPoint fromPoint = new MapRouteUtil.MapPoint();
        fromPoint.setLongitude(116.397128);
        fromPoint.setLatitude(39.916527);
        fromPoint.setCoordsys("gcj02");
        fromPoint.setCityId(1);

        MapRouteUtil.MapPoint toPoint = new MapRouteUtil.MapPoint();
        toPoint.setLongitude(116.326419);
        toPoint.setLatitude(39.896423);
        toPoint.setCoordsys("gcj02");
        toPoint.setCityId(1);

        MapRouteUtil.RouteRequest request = new MapRouteUtil.RouteRequest();
        request.setFromPoint(fromPoint);
        request.setToPoint(toPoint);
        request.setOrderId("TEST_ORDER_123");
        request.setDepartureTime(new Date());

        return request;
    }

    /**
     * 创建模拟响应
     */
    private QueryPredictRouteResponseType createMockResponse() {
        QueryPredictRouteResponseType response = new QueryPredictRouteResponseType();
        
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE);
        response.setResponseResult(responseResult);
        
        response.setDistance(10000); // 10公里，单位米
        response.setDuration(1800);  // 30分钟，单位秒
        
        return response;
    }
}
