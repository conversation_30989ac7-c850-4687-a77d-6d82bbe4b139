package com.ctrip.dcs.dsp.delay.infrastructure.util;

import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.base.Splitter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 地图路径查询配置类
 * 独立的配置管理，便于移植到其他项目
 * 
 * <AUTHOR>
 */
@Component
public class MapRouteConfig {

    private static final Logger logger = LoggerFactory.getLogger(MapRouteConfig.class);
    
    private static final Splitter DEFAULT_SPLITTER = Splitter.on(',').trimResults().omitEmptyStrings();

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    /**
     * 检查城市是否在白名单中
     * 
     * @param cityId 城市ID
     * @return true-在白名单中，false-不在白名单中
     */
    public boolean isCityInWhitelist(Integer cityId) {
        if (cityId == null) {
            return false;
        }
        
        try {
            // 如果开启了top20城市开关，则检查城市是否在top20列表中
            if (delayDspCommonQConfig.isTop20CitySwitch()) {
                List<Integer> top20Cities = delayDspCommonQConfig.top20CityIds();
                boolean inWhitelist = top20Cities.contains(cityId);
                logger.debug("MapRouteConfig.isCityInWhitelist", "cityId: {}, inTop20Whitelist: {}", cityId, inWhitelist);
                return inWhitelist;
            }
            
            // 如果没有开启top20城市开关，默认允许所有城市
            return true;
            
        } catch (Exception e) {
            logger.error("MapRouteConfig.isCityInWhitelist error for cityId: {}", cityId, e);
            // 异常时默认不允许
            return false;
        }
    }

    /**
     * 检查时段是否在白名单中
     * 
     * @param departureTime 出发时间
     * @return true-在白名单中，false-不在白名单中
     */
    public boolean isTimeInWhitelist(Date departureTime) {
        if (departureTime == null) {
            return true; // 如果没有出发时间，默认允许
        }
        
        try {
            boolean inWhitelist = delayDspCommonQConfig.isWithinCallableTimePeriod(departureTime);
            logger.debug("MapRouteConfig.isTimeInWhitelist", "departureTime: {}, inWhitelist: {}", departureTime, inWhitelist);
            return inWhitelist;
            
        } catch (Exception e) {
            logger.error("MapRouteConfig.isTimeInWhitelist error for departureTime: {}", departureTime, e);
            // 异常时默认允许
            return true;
        }
    }

    /**
     * 检查城市是否需要监控埋点
     * 
     * @param cityId 城市ID
     * @return true-需要监控，false-不需要监控
     */
    public boolean isMonitorCity(Integer cityId) {
        if (cityId == null) {
            return false;
        }
        
        try {
            boolean needMonitor = delayDspCommonQConfig.isMonitorCity(cityId);
            logger.debug("MapRouteConfig.isMonitorCity", "cityId: {}, needMonitor: {}", cityId, needMonitor);
            return needMonitor;
            
        } catch (Exception e) {
            logger.error("MapRouteConfig.isMonitorCity error for cityId: {}", cityId, e);
            return false;
        }
    }

    /**
     * 是否启用高德未来路径查询
     * 
     * @return true-启用，false-不启用
     */
    public boolean isGaodeFutureEnabled() {
        try {
            boolean enabled = delayDspCommonQConfig.isUseGaodeFuture();
            logger.debug("MapRouteConfig.isGaodeFutureEnabled", "enabled: {}", enabled);
            return enabled;
            
        } catch (Exception e) {
            logger.error("MapRouteConfig.isGaodeFutureEnabled error", e);
            return false;
        }
    }

    /**
     * 是否启用高德未来开关
     * 
     * @return true-启用，false-不启用
     */
    public boolean isGaodeFutureSwitchEnabled() {
        try {
            boolean enabled = delayDspCommonQConfig.isGaoDeFutureSwitch();
            logger.debug("MapRouteConfig.isGaodeFutureSwitchEnabled", "enabled: {}", enabled);
            return enabled;
            
        } catch (Exception e) {
            logger.error("MapRouteConfig.isGaodeFutureSwitchEnabled error", e);
            return false;
        }
    }

    /**
     * 获取未来查询的QPS限制
     * 
     * @return QPS限制值
     */
    public Integer getFuturePermitPerSecond() {
        try {
            Integer permits = delayDspCommonQConfig.getFuturePermitPerSecond();
            logger.debug("MapRouteConfig.getFuturePermitPerSecond", "permits: {}", permits);
            return permits != null ? permits : 100; // 默认100
            
        } catch (Exception e) {
            logger.error("MapRouteConfig.getFuturePermitPerSecond error", e);
            return 100;
        }
    }

    /**
     * 获取线程池核心线程数
     * 
     * @return 核心线程数
     */
    public Integer getThreadPoolCoreSize() {
        try {
            Integer coreSize = delayDspCommonQConfig.getDelayGaoDeFutureThreadCorePoolSize();
            logger.debug("MapRouteConfig.getThreadPoolCoreSize", "coreSize: {}", coreSize);
            return coreSize != null ? coreSize : 5; // 默认5
            
        } catch (Exception e) {
            logger.error("MapRouteConfig.getThreadPoolCoreSize error", e);
            return 5;
        }
    }

    /**
     * 获取线程池最大线程数
     * 
     * @return 最大线程数
     */
    public Integer getThreadPoolMaxSize() {
        try {
            Integer maxSize = delayDspCommonQConfig.getDelayGaoDeFutureThreadMaxPoolSize();
            logger.debug("MapRouteConfig.getThreadPoolMaxSize", "maxSize: {}", maxSize);
            return maxSize != null ? maxSize : 200; // 默认200
            
        } catch (Exception e) {
            logger.error("MapRouteConfig.getThreadPoolMaxSize error", e);
            return 200;
        }
    }

    /**
     * 获取队列大小
     * 
     * @return 队列大小
     */
    public Integer getQueueSize() {
        try {
            Integer queueSize = delayDspCommonQConfig.getDelayQueueSize();
            logger.debug("MapRouteConfig.getQueueSize", "queueSize: {}", queueSize);
            return queueSize != null ? queueSize : 1000; // 默认1000
            
        } catch (Exception e) {
            logger.error("MapRouteConfig.getQueueSize error", e);
            return 1000;
        }
    }

    /**
     * 获取线程保活时间
     * 
     * @return 保活时间（毫秒）
     */
    public Long getKeepAliveTime() {
        try {
            Long keepAliveTime = delayDspCommonQConfig.getKeepAliveTime();
            logger.debug("MapRouteConfig.getKeepAliveTime", "keepAliveTime: {}", keepAliveTime);
            return keepAliveTime != null ? keepAliveTime : 200L; // 默认200ms
            
        } catch (Exception e) {
            logger.error("MapRouteConfig.getKeepAliveTime error", e);
            return 200L;
        }
    }

    /**
     * 检查请求是否应该使用高德未来路径查询
     * 综合判断各种开关和白名单条件
     * 
     * @param cityId 城市ID
     * @param departureTime 出发时间
     * @return true-应该使用，false-不应该使用
     */
    public boolean shouldUseGaodeFuture(Integer cityId, Date departureTime) {
        try {
            // 检查基础开关
            if (!isGaodeFutureEnabled()) {
                logger.debug("MapRouteConfig.shouldUseGaodeFuture", "gaodeFuture is disabled");
                return false;
            }
            
            if (!isGaodeFutureSwitchEnabled()) {
                logger.debug("MapRouteConfig.shouldUseGaodeFuture", "gaodeFutureSwitch is disabled");
                return false;
            }
            
            // 检查出发时间
            if (departureTime == null) {
                logger.debug("MapRouteConfig.shouldUseGaodeFuture", "departureTime is null");
                return false;
            }
            
            // 检查城市白名单
            if (!isCityInWhitelist(cityId)) {
                logger.debug("MapRouteConfig.shouldUseGaodeFuture", "cityId {} is not in whitelist", cityId);
                return false;
            }
            
            // 检查时段白名单
            if (!isTimeInWhitelist(departureTime)) {
                logger.debug("MapRouteConfig.shouldUseGaodeFuture", "departureTime {} is not in whitelist", departureTime);
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            logger.error("MapRouteConfig.shouldUseGaodeFuture error", e);
            return false;
        }
    }
}
