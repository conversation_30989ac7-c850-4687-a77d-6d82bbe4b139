package com.ctrip.dcs.dsp.delay.infrastructure.util;

import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.util.GeoHashUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * MapRoute工厂类
 * 提供便捷的对象创建方法，简化MapRouteUtil的使用
 * 
 * <AUTHOR>
 */
public class MapRouteFactory {

    private static final Logger logger = LoggerFactory.getLogger(MapRouteFactory.class);

    /**
     * 创建地图点位对象
     * 
     * @param longitude 经度
     * @param latitude 纬度
     * @param coordsys 坐标系类型
     * @param cityId 城市ID
     * @return MapPoint对象
     */
    public static MapRouteUtil.MapPoint createMapPoint(Double longitude, Double latitude, 
                                                      String coordsys, Integer cityId) {
        MapRouteUtil.MapPoint point = new MapRouteUtil.MapPoint();
        point.setLongitude(longitude);
        point.setLatitude(latitude);
        point.setCoordsys(coordsys);
        point.setCityId(cityId);
        return point;
    }

    /**
     * 从订单创建地图点位对象（起点）
     * 
     * @param order 订单对象
     * @return MapPoint对象
     */
    public static MapRouteUtil.MapPoint createFromPointFromOrder(DelayDspOrder order) {
        if (order == null) {
            return null;
        }
        
        return createMapPoint(
            order.getFromLongitude(),
            order.getFromLatitude(),
            order.getFromCoordsys(),
            order.getCityId()
        );
    }

    /**
     * 从订单创建地图点位对象（终点）
     * 
     * @param order 订单对象
     * @return MapPoint对象
     */
    public static MapRouteUtil.MapPoint createToPointFromOrder(DelayDspOrder order) {
        if (order == null) {
            return null;
        }
        
        return createMapPoint(
            order.getToLongitude(),
            order.getToLatitude(),
            order.getToCoordsys(),
            order.getCityId()
        );
    }

    /**
     * 从司机创建地图点位对象
     * 
     * @param driver 司机对象
     * @param cityId 城市ID
     * @param coordsys 坐标系类型
     * @return MapPoint对象
     */
    public static MapRouteUtil.MapPoint createPointFromDriver(Driver driver, Integer cityId, String coordsys) {
        if (driver == null) {
            return null;
        }
        
        return createMapPoint(
            driver.getAddressLongitude(),
            driver.getAddressLatitude(),
            coordsys,
            cityId
        );
    }

    /**
     * 创建路径查询请求
     * 
     * @param fromPoint 起点
     * @param toPoint 终点
     * @param orderId 订单ID
     * @param departureTime 出发时间
     * @return RouteRequest对象
     */
    public static MapRouteUtil.RouteRequest createRouteRequest(MapRouteUtil.MapPoint fromPoint,
                                                              MapRouteUtil.MapPoint toPoint,
                                                              String orderId,
                                                              Date departureTime) {
        MapRouteUtil.RouteRequest request = new MapRouteUtil.RouteRequest();
        request.setFromPoint(fromPoint);
        request.setToPoint(toPoint);
        request.setOrderId(orderId);
        request.setDepartureTime(departureTime);
        return request;
    }

    /**
     * 从订单创建路径查询请求（订单起点到终点）
     * 
     * @param order 订单对象
     * @param departureTime 出发时间
     * @return RouteRequest对象
     */
    public static MapRouteUtil.RouteRequest createRouteRequestFromOrder(DelayDspOrder order, Date departureTime) {
        if (order == null) {
            return null;
        }
        
        MapRouteUtil.MapPoint fromPoint = createFromPointFromOrder(order);
        MapRouteUtil.MapPoint toPoint = createToPointFromOrder(order);
        
        return createRouteRequest(fromPoint, toPoint, order.getOrderId(), departureTime);
    }

    /**
     * 创建司机到订单起点的路径查询请求
     * 
     * @param driver 司机对象
     * @param order 订单对象
     * @param departureTime 出发时间
     * @return RouteRequest对象
     */
    public static MapRouteUtil.RouteRequest createDriverToOrderRequest(Driver driver, DelayDspOrder order, Date departureTime) {
        if (driver == null || order == null) {
            return null;
        }
        
        MapRouteUtil.MapPoint fromPoint = createPointFromDriver(driver, order.getCityId(), order.getFromCoordsys());
        MapRouteUtil.MapPoint toPoint = createFromPointFromOrder(order);
        
        return createRouteRequest(fromPoint, toPoint, order.getOrderId(), departureTime);
    }

    /**
     * 创建订单终点到司机位置的路径查询请求
     * 
     * @param order 订单对象
     * @param driver 司机对象
     * @param departureTime 出发时间
     * @return RouteRequest对象
     */
    public static MapRouteUtil.RouteRequest createOrderToDriverRequest(DelayDspOrder order, Driver driver, Date departureTime) {
        if (order == null || driver == null) {
            return null;
        }
        
        MapRouteUtil.MapPoint fromPoint = createToPointFromOrder(order);
        MapRouteUtil.MapPoint toPoint = createPointFromDriver(driver, order.getCityId(), order.getFromCoordsys());
        
        return createRouteRequest(fromPoint, toPoint, order.getOrderId(), departureTime);
    }

    /**
     * 创建订单间的路径查询请求（第一个订单的终点到第二个订单的起点）
     * 
     * @param order1 第一个订单
     * @param order2 第二个订单
     * @param departureTime 出发时间
     * @return RouteRequest对象
     */
    public static MapRouteUtil.RouteRequest createOrderToOrderRequest(DelayDspOrder order1, DelayDspOrder order2, Date departureTime) {
        if (order1 == null || order2 == null) {
            return null;
        }
        
        MapRouteUtil.MapPoint fromPoint = createToPointFromOrder(order1);
        MapRouteUtil.MapPoint toPoint = createFromPointFromOrder(order2);
        
        return createRouteRequest(fromPoint, toPoint, order2.getOrderId(), departureTime);
    }

    /**
     * 批量创建路径查询请求
     * 
     * @param orders 订单列表
     * @param departureTime 出发时间
     * @return RouteRequest列表
     */
    public static List<MapRouteUtil.RouteRequest> createBatchRouteRequestsFromOrders(List<DelayDspOrder> orders, Date departureTime) {
        List<MapRouteUtil.RouteRequest> requests = new ArrayList<>();
        
        if (orders == null || orders.isEmpty()) {
            return requests;
        }
        
        for (DelayDspOrder order : orders) {
            try {
                MapRouteUtil.RouteRequest request = createRouteRequestFromOrder(order, departureTime);
                if (request != null) {
                    requests.add(request);
                }
            } catch (Exception e) {
                logger.error("MapRouteFactory.createBatchRouteRequestsFromOrders error for order: {}", 
                    order != null ? order.getOrderId() : "null", e);
            }
        }
        
        return requests;
    }

    /**
     * 验证地图点位是否有效
     * 
     * @param point 地图点位
     * @return true-有效，false-无效
     */
    public static boolean isValidMapPoint(MapRouteUtil.MapPoint point) {
        if (point == null) {
            return false;
        }
        
        if (point.getLongitude() == null || point.getLatitude() == null) {
            return false;
        }
        
        if (point.getCityId() == null) {
            return false;
        }
        
        if (StringUtils.isBlank(point.getCoordsys())) {
            return false;
        }
        
        // 检查经纬度范围
        if (point.getLongitude() < -180 || point.getLongitude() > 180) {
            return false;
        }
        
        if (point.getLatitude() < -90 || point.getLatitude() > 90) {
            return false;
        }
        
        return true;
    }

    /**
     * 验证路径查询请求是否有效
     * 
     * @param request 路径查询请求
     * @return true-有效，false-无效
     */
    public static boolean isValidRouteRequest(MapRouteUtil.RouteRequest request) {
        if (request == null) {
            return false;
        }
        
        if (!isValidMapPoint(request.getFromPoint())) {
            return false;
        }
        
        if (!isValidMapPoint(request.getToPoint())) {
            return false;
        }
        
        if (StringUtils.isBlank(request.getOrderId())) {
            return false;
        }
        
        return true;
    }

    /**
     * 计算两个地图点位之间的直线距离（单位：公里）
     * 使用Haversine公式计算球面距离
     * 
     * @param point1 第一个点位
     * @param point2 第二个点位
     * @return 直线距离（公里），如果计算失败返回-1
     */
    public static double calculateStraightLineDistance(MapRouteUtil.MapPoint point1, MapRouteUtil.MapPoint point2) {
        if (!isValidMapPoint(point1) || !isValidMapPoint(point2)) {
            return -1;
        }
        
        try {
            double lat1 = Math.toRadians(point1.getLatitude());
            double lon1 = Math.toRadians(point1.getLongitude());
            double lat2 = Math.toRadians(point2.getLatitude());
            double lon2 = Math.toRadians(point2.getLongitude());
            
            double dlat = lat2 - lat1;
            double dlon = lon2 - lon1;
            
            double a = Math.sin(dlat / 2) * Math.sin(dlat / 2) +
                      Math.cos(lat1) * Math.cos(lat2) *
                      Math.sin(dlon / 2) * Math.sin(dlon / 2);
            
            double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            
            // 地球半径（公里）
            double earthRadius = 6371;
            
            return earthRadius * c;
            
        } catch (Exception e) {
            logger.error("MapRouteFactory.calculateStraightLineDistance error", e);
            return -1;
        }
    }

    /**
     * 生成地图点位的GeoHash
     * 
     * @param point 地图点位
     * @return GeoHash字符串，如果生成失败返回null
     */
    public static String generateGeoHash(MapRouteUtil.MapPoint point) {
        if (!isValidMapPoint(point)) {
            return null;
        }
        
        try {
            return GeoHashUtil.buildGeoHash(point.getLongitude(), point.getLatitude());
        } catch (Exception e) {
            logger.error("MapRouteFactory.generateGeoHash error", e);
            return null;
        }
    }
}
